import os
import shutil
import torch
import gc
from fastapi import FastAPI, UploadFile, File, Form
from fastapi.responses import FileResponse
from tempfile import NamedTemporaryFile
import time
from huggingface_hub import snapshot_download
from models.vc.vevo.vevo_utils import VevoInferencePipeline, save_audio
from pydub import AudioSegment
import soundfile as sf
from df.enhance import init_df, enhance

torch.cuda.empty_cache()
app = FastAPI(title="Vevo API")

# ===== Device =====
device = torch.device("cuda") if torch.cuda.is_available() else torch.device("cpu")

# Global variables for lazy loading
timbre_pipeline = None
tts_pipeline = None
denoise_model = None
denoise_df_state = None

# ===== Model Paths Setup (Download but don't load models yet) =====
def setup_model_paths():
    """Download model files and setup paths without loading models"""
    tokenizer_dir = snapshot_download(
        repo_id="amphion/Vevo",
        repo_type="model",
        cache_dir="./ckpts/Vevo",
        allow_patterns=["tokenizer/vq8192/*"],
    )
    tokenizer_ckpt_path = os.path.join(tokenizer_dir, "tokenizer/vq8192")

    fmt_dir = snapshot_download(
        repo_id="amphion/Vevo",
        repo_type="model",
        cache_dir="./ckpts/Vevo",
        allow_patterns=["acoustic_modeling/Vq8192ToMels/*"],
    )
    fmt_cfg_path = "./models/vc/vevo/config/Vq8192ToMels.json"
    fmt_ckpt_path = os.path.join(fmt_dir, "acoustic_modeling/Vq8192ToMels")

    vocoder_dir = snapshot_download(
        repo_id="amphion/Vevo",
        repo_type="model",
        cache_dir="./ckpts/Vevo",
        allow_patterns=["acoustic_modeling/Vocoder/*"],
    )
    vocoder_cfg_path = "./models/vc/vevo/config/Vocoder.json"
    vocoder_ckpt_path = os.path.join(vocoder_dir, "acoustic_modeling/Vocoder")

    # TTS Model Setup
    ar_dir = snapshot_download(
        repo_id="amphion/Vevo",
        repo_type="model",
        cache_dir="./ckpts/Vevo",
        allow_patterns=["contentstyle_modeling/PhoneToVq8192/*"],
    )
    ar_cfg_path = "./models/vc/vevo/config/PhoneToVq8192.json"
    ar_ckpt_path = os.path.join(ar_dir, "contentstyle_modeling/PhoneToVq8192")

    return {
        'tokenizer_ckpt_path': tokenizer_ckpt_path,
        'fmt_cfg_path': fmt_cfg_path,
        'fmt_ckpt_path': fmt_ckpt_path,
        'vocoder_cfg_path': vocoder_cfg_path,
        'vocoder_ckpt_path': vocoder_ckpt_path,
        'ar_cfg_path': ar_cfg_path,
        'ar_ckpt_path': ar_ckpt_path,
    }

# Setup model paths
model_paths = setup_model_paths()


# ===== Noise Cancellation Functions =====
def get_denoise_model():
    """Lazy load DeepFilterNet model"""
    global denoise_model, denoise_df_state
    if denoise_model is None:
        denoise_model, denoise_df_state, _ = init_df()
    return denoise_model, denoise_df_state


def convert_to_wav(input_path, temp_wav="temp_input.wav"):
    """Convert audio file to WAV format with proper settings"""
    audio = AudioSegment.from_file(input_path)
    audio = audio.set_channels(1).set_frame_rate(48000)
    audio.export(temp_wav, format="wav")
    print(f"🔄 Converted to WAV: {temp_wav}")
    return temp_wav


def load_wav_tensor(wav_path, sr=48000):
    """Load WAV file as tensor"""
    data, file_sr = sf.read(wav_path)
    if file_sr != sr:
        raise ValueError(f"Sample rate is {file_sr}, expected {sr}")
    tensor = torch.from_numpy(data).float().unsqueeze(0)
    return tensor, sr


def denoise_tensor(input_tensor, model, df_state):
    """Apply noise cancellation to audio tensor"""
    input_tensor = input_tensor.cpu()  # Ensure tensor is on CPU
    with torch.no_grad():
        output = enhance(model, df_state, input_tensor)
    return output.squeeze(0).numpy()


def apply_noise_cancellation(input_audio_path, output_audio_path):
    """Apply noise cancellation to audio file"""
    temp_wav = convert_to_wav(input_audio_path)
    model, df_state = get_denoise_model()
    tensor, sr = load_wav_tensor(temp_wav, sr=df_state.sr())
    enhanced = denoise_tensor(tensor, model, df_state)
    sf.write(output_audio_path, enhanced, sr)
    print(f"✅ Clean audio saved to: {output_audio_path}")
    os.remove(temp_wav)
    return output_audio_path


def get_timbre_pipeline():
    """Lazy load timbre transfer pipeline"""
    global timbre_pipeline
    if timbre_pipeline is None:
        # Clear GPU cache before loading
        torch.cuda.empty_cache()
        gc.collect()

        timbre_pipeline = VevoInferencePipeline(
            content_style_tokenizer_ckpt_path=model_paths['tokenizer_ckpt_path'],
            fmt_cfg_path=model_paths['fmt_cfg_path'],
            fmt_ckpt_path=model_paths['fmt_ckpt_path'],
            vocoder_cfg_path=model_paths['vocoder_cfg_path'],
            vocoder_ckpt_path=model_paths['vocoder_ckpt_path'],
            device=device,
        )
    return timbre_pipeline


def get_tts_pipeline():
    """Lazy load TTS pipeline"""
    global tts_pipeline
    if tts_pipeline is None:
        # Clear GPU cache before loading
        torch.cuda.empty_cache()
        gc.collect()

        tts_pipeline = VevoInferencePipeline(
            content_style_tokenizer_ckpt_path=model_paths['tokenizer_ckpt_path'],
            ar_cfg_path=model_paths['ar_cfg_path'],
            ar_ckpt_path=model_paths['ar_ckpt_path'],
            fmt_cfg_path=model_paths['fmt_cfg_path'],
            fmt_ckpt_path=model_paths['fmt_ckpt_path'],
            vocoder_cfg_path=model_paths['vocoder_cfg_path'],
            vocoder_ckpt_path=model_paths['vocoder_ckpt_path'],
            device=device,
        )
    return tts_pipeline


def clear_unused_pipeline(keep_pipeline):
    """Clear the pipeline that's not being used to free GPU memory"""
    global timbre_pipeline, tts_pipeline

    if keep_pipeline == "timbre" and tts_pipeline is not None:
        del tts_pipeline
        tts_pipeline = None
    elif keep_pipeline == "tts" and timbre_pipeline is not None:
        del timbre_pipeline
        timbre_pipeline = None

    torch.cuda.empty_cache()
    gc.collect()


def vevo_timbre(content_wav_path, reference_wav_path, output_path):
    # Clear TTS pipeline to free memory for timbre transfer
    clear_unused_pipeline("timbre")

    pipeline = get_timbre_pipeline()
    gen_audio = pipeline.inference_fm(
        src_wav_path=content_wav_path,
        timbre_ref_wav_path=reference_wav_path,
        flow_matching_steps=32,
    )
    save_audio(gen_audio, output_path=output_path)


def vevo_tts(
    src_text,
    ref_wav_path,
    timbre_ref_wav_path=None,
    output_path=None,
    ref_text=None,
    src_language="en",
    ref_language="en",
):
    # Clear timbre pipeline to free memory for TTS
    clear_unused_pipeline("tts")

    if timbre_ref_wav_path is None:
        timbre_ref_wav_path = ref_wav_path

    pipeline = get_tts_pipeline()
    gen_audio = pipeline.inference_ar_and_fm(
        src_wav_path=None,
        src_text=src_text,
        style_ref_wav_path=ref_wav_path,
        timbre_ref_wav_path=timbre_ref_wav_path,
        style_ref_wav_text=ref_text,
        src_text_language=src_language,
        style_ref_wav_text_language=ref_language,
    )

    assert output_path is not None
    save_audio(gen_audio, output_path=output_path)


@app.post("/timbre-transfer/")
async def timbre_transfer(
    source_audio: UploadFile = File(...),
    reference_audio: UploadFile = File(...),
):
    with NamedTemporaryFile(delete=False, suffix=".wav") as src_temp, \
         NamedTemporaryFile(delete=False, suffix=".wav") as ref_temp, \
         NamedTemporaryFile(delete=False, suffix=".wav") as out_temp:

        shutil.copyfileobj(source_audio.file, src_temp)
        shutil.copyfileobj(reference_audio.file, ref_temp)

        src_path = src_temp.name
        ref_path = ref_temp.name
        out_path = out_temp.name

    start_time= time.time()
# Run inference
    vevo_timbre(src_path, ref_path, out_path)
    end_time = time.time()
    latency = end_time - start_time

    response = FileResponse(out_path, media_type="audio/wav", filename="output.wav")
    response.headers["X-Inference-Latency"] = str(latency)

    # Clean up temporary files
    try:
        os.remove(src_path)
        os.remove(ref_path)
    except OSError:
        pass  # Files may already be cleaned up

    return response


@app.post("/text-to-speech/")
async def text_to_speech(
    text: str = Form(...),
    reference_audio: UploadFile = File(...),
    reference_text: str = Form(None),
    timbre_audio: UploadFile = File(None),
    src_language: str = Form("en"),
    ref_language: str = Form("en"),
    noise_cancellation: bool = Form(False),
):
    with NamedTemporaryFile(delete=False, suffix=".wav") as ref_temp, \
         NamedTemporaryFile(delete=False, suffix=".wav") as out_temp:

        # Save reference audio
        shutil.copyfileobj(reference_audio.file, ref_temp)
        ref_path = ref_temp.name
        out_path = out_temp.name

        # Handle optional timbre audio
        timbre_path = None
        if timbre_audio:
            with NamedTemporaryFile(delete=False, suffix=".wav") as timbre_temp:
                shutil.copyfileobj(timbre_audio.file, timbre_temp)
                timbre_path = timbre_temp.name

                # Apply noise cancellation if requested
                if noise_cancellation:
                    with NamedTemporaryFile(delete=False, suffix=".wav") as denoised_temp:
                        denoised_path = denoised_temp.name
                    apply_noise_cancellation(timbre_path, denoised_path)
                    # Clean up original file and use denoised version
                    os.remove(timbre_path)
                    timbre_path = denoised_path

    start_time = time.time()
    # Run TTS inference
    vevo_tts(
        src_text=text,
        ref_wav_path=ref_path,
        timbre_ref_wav_path=timbre_path,
        output_path=out_path,
        ref_text=reference_text,
        src_language=src_language,
        ref_language=ref_language,
    )
    end_time = time.time()
    latency = end_time - start_time

    response = FileResponse(out_path, media_type="audio/wav", filename="tts_output.wav")
    response.headers["X-Inference-Latency"] = str(latency)

    # Clean up temporary files
    try:
        os.remove(ref_path)
        if timbre_path:
            os.remove(timbre_path)
    except OSError:
        pass  # Files may already be cleaned up

    return response
