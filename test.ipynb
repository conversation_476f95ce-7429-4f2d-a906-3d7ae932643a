import librosa
import noisereduce as nr
import soundfile as sf

# Load audio file
audio_path = "noisybishanteng.mp3"
y, sr = librosa.load(audio_path, sr=None)

# Perform noise reduction
reduced_noise = nr.reduce_noise(y=y, sr=sr)

# Save the cleaned audio
sf.write("denoised_audio.wav", reduced_noise, sr)


import os
from pydub import AudioSegment
import torch
import soundfile as sf
from df.enhance import init_df, enhance

def convert_to_wav(input_path, temp_wav="temp_input.wav"):
    audio = AudioSegment.from_file(input_path)
    audio = audio.set_channels(1).set_frame_rate(48000)
    audio.export(temp_wav, format="wav")
    print(f"🔄 Converted to WAV: {temp_wav}")
    return temp_wav

def load_wav_tensor(wav_path, sr=48000):
    data, file_sr = sf.read(wav_path)
    if file_sr != sr:
        raise ValueError(f"Sample rate is {file_sr}, expected {sr}")
    tensor = torch.from_numpy(data).float().unsqueeze(0)
    return tensor, sr

def denoise_tensor(input_tensor, model, df_state):
    input_tensor = input_tensor.cpu()  # 💡 FIX: ensure tensor is on CPU
    with torch.no_grad():
        output = enhance(model, df_state, input_tensor)
    return output.squeeze(0).numpy()

def main(input_audio, output_audio):
    temp_wav = convert_to_wav(input_audio)
    model, df_state, _ = init_df()
    tensor, sr = load_wav_tensor(temp_wav, sr=df_state.sr())
    enhanced = denoise_tensor(tensor, model, df_state)
    sf.write(output_audio, enhanced, sr)
    print(f"✅ Clean audio saved to: {output_audio}")
    os.remove(temp_wav)

# 🔊 Set your input and output filenames here
if __name__ == "__main__":
    input_file = "bibek_bandipur_eng.mp3"
    output_file = "output.wav"
    main(input_file, output_file)
