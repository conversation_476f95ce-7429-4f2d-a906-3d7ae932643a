# DeepFilterNet Noise Canceling Sink
#
# 1. Copy this file into a conf.d/ directory such as:
#     /etc/pipewire/filter-chain.conf.d/
#     or locally:
#     ~/.config/pipewire/filter-chain.conf.d/
#
# 2. Install the deep_filter plugin globally to:
#     /usr/lib/ladspa/libdeep_filter_ladspa.so
#     or locally:
#     ~/.ladspa/libdeep_filter_ladspa.so
#
# 3. Adjust the plugin path (line 38) accordingly. If you installed the plugin
#     locally in your home directory, make sure to provide an absolute path here
#     (i.e. /home/<USER>/.ladspa/) and do not use '~' or '$HOME'!
#
# 4. You may limit the noise attenuation (i.e. don't reduce all noise) by setting
#     'Attenuation Limit (dB)' to a smaller value. For little noise reduction, try
#     6-12, for medium noise reduction 18-24 dB. 100 dB means no attenuation limit.
#
# 5. Run the DeepFilter pipewire filter via `pipewire -c filter-chain.conf`
#
# 6. Select the DeepFilter sink (e.g. in pavucontrol) for an application that
#     currently plays some audio (i.e. <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Discord, etc.).
#     DeepFilterNet will denoise the application output and will playback the
#     enhanced audio on your default sink (speakers, headphone).

context.modules = [
    { name = libpipewire-module-filter-chain
        args = {
            node.description = "DeepFilter Noise Canceling Sink"
            media.name       = "DeepFilter Noise Canceling Sink"
            filter.graph = {
                nodes = [
                    {
                        type   = ladspa
                        name   = "DeepFilter Stereo"
                        plugin = /usr/lib/ladspa/libdeep_filter_ladspa.so
                        label  = deep_filter_stereo
                        control = {
                            "Attenuation Limit (dB)" 100
                        }
                    }
                ]
            }
            audio.rate = 48000
            audio.channels = 2
            audio.position = [FL FR]
            capture.props = {
                node.name = "deep_filter_stereo_input"
                media.class = Audio/Sink
            }
            playback.props = {
                node.name = "deep_filter_stereo_output"
                node.passive = true
            }
        }
    }
]
